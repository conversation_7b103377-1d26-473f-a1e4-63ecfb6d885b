import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { Menu } from './Menu';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';

type Story = StoryObj<typeof Menu>;

const meta: Meta<typeof Menu> = {
  title: 'UI/Menu',
  component: Menu,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
    },
    side: {
      control: 'select',
      options: ['top', 'right', 'bottom', 'left'],
    },
    align: {
      control: 'select',
      options: ['start', 'center', 'end'],
    },
  },
};

export default meta;

export const Default: Story = {
  args: {
    trigger: <Button variant="default">Open Menu</Button>,
    children: (
      <>
        <Menu.Item onClick={() => console.log('Edit clicked')}>Edit</Menu.Item>
        <Menu.Item onClick={() => console.log('Delete clicked')}>
          Delete
        </Menu.Item>
        <Menu.Separator />
        <Menu.Item onClick={() => console.log('Settings clicked')}>
          Settings
        </Menu.Item>
      </>
    ),
  },
};

export const WithIcon: Story = {
  args: {
    trigger: (
      <Button variant="unstyled" aria-label="More options">
        <Icon name="moreOptions" color="#333" size="1.4rem" />
      </Button>
    ),
    children: (
      <>
        <Menu.Item onClick={() => console.log('Edit Credentials clicked')}>
          <p className="text-sm font-medium text-black">Edit Credentials</p>
        </Menu.Item>
        <Menu.Item onClick={() => console.log('Shipping Information clicked')}>
          <p className="text-sm font-medium text-black">Shipping Information</p>
        </Menu.Item>
      </>
    ),
  },
};

export const WithLabel: Story = {
  args: {
    trigger: <Button variant="secondary">Actions</Button>,
    children: (
      <>
        <Menu.Label>Account</Menu.Label>
        <Menu.Item onClick={() => console.log('Profile clicked')}>
          Profile
        </Menu.Item>
        <Menu.Item onClick={() => console.log('Settings clicked')}>
          Settings
        </Menu.Item>
        <Menu.Separator />
        <Menu.Label>Danger Zone</Menu.Label>
        <Menu.Item onClick={() => console.log('Delete clicked')}>
          <span className="text-red-600">Delete Account</span>
        </Menu.Item>
      </>
    ),
  },
};

export const Sizes: Story = {
  render: () => (
    <div className="flex gap-4">
      <Menu size="sm" trigger={<Button size="sm">Small Menu</Button>}>
        <Menu.Item>Small Item</Menu.Item>
        <Menu.Item>Another Item</Menu.Item>
      </Menu>

      <Menu size="md" trigger={<Button>Medium Menu</Button>}>
        <Menu.Item>Medium Item</Menu.Item>
        <Menu.Item>Another Item</Menu.Item>
      </Menu>

      <Menu size="lg" trigger={<Button>Large Menu</Button>}>
        <Menu.Item>Large Item</Menu.Item>
        <Menu.Item>Another Item</Menu.Item>
      </Menu>
    </div>
  ),
};

export const Positioning: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-8 p-8">
      <Menu side="top" align="start" trigger={<Button>Top Start</Button>}>
        <Menu.Item>Item 1</Menu.Item>
        <Menu.Item>Item 2</Menu.Item>
      </Menu>

      <Menu side="top" align="end" trigger={<Button>Top End</Button>}>
        <Menu.Item>Item 1</Menu.Item>
        <Menu.Item>Item 2</Menu.Item>
      </Menu>

      <Menu side="bottom" align="start" trigger={<Button>Bottom Start</Button>}>
        <Menu.Item>Item 1</Menu.Item>
        <Menu.Item>Item 2</Menu.Item>
      </Menu>

      <Menu side="bottom" align="end" trigger={<Button>Bottom End</Button>}>
        <Menu.Item>Item 1</Menu.Item>
        <Menu.Item>Item 2</Menu.Item>
      </Menu>
    </div>
  ),
};
