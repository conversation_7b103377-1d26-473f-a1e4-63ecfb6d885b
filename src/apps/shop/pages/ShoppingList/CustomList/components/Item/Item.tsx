import { OfferType, ProductType } from '@/types';
import { Actions } from '../Actions';
import { Badge } from '@/libs/ui/Badge/Badge';
import { Dollar } from '@/libs/products/components/Dollar/Dollar';

type ItemProps = {
  product: ProductType;
  currentOfferId: string;
  onSwap: (newOfferId: string) => void;
  originalPrice: number;
  salePrice: number;
  currentOffer: OfferType;
};

export const Item = ({
  product,
  currentOfferId,
  onSwap,
  originalPrice,
  salePrice,
  currentOffer,
}: ItemProps) => {
  return (
    <div className="flex h-24 w-full justify-between rounded-lg border border-black/[0.06] bg-white p-4">
      <div className="flex flex-col items-center gap-1">
        <div className="mr-auto flex items-center gap-2">
          <Dollar toolTipLabel="Promotion Type: Bogo" size="0.7rem" />
          <Badge className="h-4 bg-[#ED1F22] text-[10px] text-white">
            Surgery Room
          </Badge>
        </div>
        <span className="text-sm font-semibold">
          OstiFen (Carprofen) Injection for Dogs, 25mL
        </span>
        <div className="mr-auto flex">
          <span className="text-xs font-semibold">
            {currentOffer.vendor.name}
          </span>
          <div className="divider-v"></div>
          <span className="text-xs text-black/50">
            SKU: <span className="text-black">{currentOffer.vendorSku}</span>
          </span>
          <div className="divider-v"></div>
          <span className="text-xs text-black/50">
            Per unit:{' '}
            {originalPrice > salePrice ? (
              <>
                <span className="mx-1 text-xs font-semibold text-black">
                  ${salePrice}
                </span>
                <span className="text-sxs line-through">${originalPrice}</span>
              </>
            ) : (
              <>
                <span className="text-sxs line-through">{originalPrice}</span>
                <span className="text-xs">{salePrice}</span>
              </>
            )}{' '}
          </span>
        </div>
      </div>
      <Actions
        product={product}
        currentOfferId={currentOfferId}
        originalPrice={originalPrice}
        salePrice={salePrice}
        currentOffer={currentOffer}
        onSwap={onSwap}
      />
    </div>
  );
};
