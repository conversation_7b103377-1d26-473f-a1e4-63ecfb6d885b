import { VendorSwap } from '@/libs/products/components/VendorSwap/VendorSwap';
import { Icon } from '@/libs/icons/Icon';
import { getPriceString } from '@/utils';
import { AddToCart } from '@/libs/ui/AddToCart/AddToCart';
import { OfferType, ProductType } from '@/types';
import { PurchaseHistoryChart } from '@/libs/products/components/PurchaseHistory/PurchaseHistoryChart';
import { Modal } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';

type ActionProps = {
  product: ProductType;
  currentOfferId: string;
  onSwap: (newOfferId: string) => void;
  originalPrice: number;
  salePrice: number;
  currentOffer: OfferType;
};

export const Actions = ({
  product,
  currentOfferId,
  onSwap,
  originalPrice,
  salePrice,
  currentOffer,
}: ActionProps) => {
  const [isModalOpen, { open, close }] = useDisclosure(false);
  const { offersMapData } = useCartStore();
  const cartOffer = offersMapData[currentOfferId] || { quantity: 1 };

  return (
    <div className="flex h-full items-center gap-8 rounded-sm border border-cyan-100/25 bg-cyan-200 bg-gradient-to-t from-white/80 to-white/80 px-4 py-3">
      <VendorSwap
        currentOfferId={currentOfferId}
        offers={product.offers}
        onSwap={onSwap}
        className="mr-0 h-8 bg-white"
      />
      <div className="divider-v h-7"></div>
      <Icon name="graphics" size="3rem" color="#518EF8" onClick={open} />
      <Modal
        opened={isModalOpen}
        onClose={close}
        title="Purchase History"
        size="auto"
      >
        <div className="p-4">
          <PurchaseHistoryChart productId={currentOffer.id} />
        </div>
      </Modal>
      <div className="divider-v h-7"></div>
      {originalPrice > salePrice ? (
        <div className="flex flex-col items-end">
          <span className="text-xs text-black/40 line-through">
            {getPriceString(cartOffer.quantity * originalPrice)}
          </span>
          <span className="text-[16px] leading-4 font-medium">
            {getPriceString(cartOffer.quantity * salePrice)}
          </span>
        </div>
      ) : (
        <span className="text-sm font-medium">
          {getPriceString(cartOffer.quantity * salePrice)}
        </span>
      )}
      <AddToCart
        productOfferId={currentOffer.id}
        minIncrement={currentOffer.increments || 1}
        className="w-28"
      />
    </div>
  );
};
