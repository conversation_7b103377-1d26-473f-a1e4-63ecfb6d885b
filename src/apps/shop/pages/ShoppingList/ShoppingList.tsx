import { useState } from 'react';
import { Tabs } from '@/libs/ui/Tabs/Tabs';
import { SuggestedOfferItem } from '@/libs/products/components/SuggestedOfferList/SuggestedOfferItem';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { OfferType } from '@/types';
import { Item } from './CustomList/components/Item/Item';

const product = {
  id: '9ed26eac-7240-4020-9366-fe04290b77c3',
  name: 'Simparica 6 x 5mg Gold 2.8 - 5.5lbs',
  imageUrl:
    'https/shop.zoetis.co/zb2/media/300Wx300H-null?context=bWFzdGVyfHJvb3R8MjQ3NDN8aW1hZ2UvanBlZ3xhREZrTDJneVlpOHhNalUxTmpRMk1EWTROek01TUM4ek1EQlhlRE13TUVoZmJuVnNiQXwyNjJkZDg5OWQ1ZDZhN2NmMTUzMzU0NzNmNDEzZTQ5ODk0MjFhYjYyZmY4ZWUyYzA2NTIwY2JlNTQ2MTc1YzU1',
  isFavorite: false,
  manufacturer: null,
  manufacturerSku: '10012457',
  description:
    'Persistent Protection from ticks and fleasnSimparicu00au00a0(sarolaneru00a0is FDA-approved to block infections that may cause Lyme disease as a result of killinu00a0ixodes scapularis.',
  offers: [
    {
      id: '9d7581c7-ebe0-468e-8b8a-6221b3476f70',
      vendor: {
        id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
        name: 'Zoetis',
        imageUrl:
          'https/staging.services.highfive.ve/storag/vendor-image/zoetis.png',
        type: 'manufacturer',
      },
      isPurchasable: true,
      vendorSku: '10012457',
      price: '75.35',
      clinicPrice: null,
      stockStatus: 'IN_STOCK',
      lastOrderedAt: '2025-09-09T16:43:13.000000Z',
      lastOrderedQuantity: 2,
      increments: 1,
      isRecommended: true,
      rebatePercent: '10.0',
      unitOfMeasure: null,
      size: null,
      rawCategory1: null,
      rawCategory2: null,
      rawCategory3: null,
      rawCategory4: null,
    },
  ],
  attributes: [],
  isHazardous: false,
  requiresPrescription: false,
  requiresColdShipping: false,
  isControlledSubstance: false,
  isControlled222Form: false,
  requiresPedigree: false,
  nationalDrugCode: null,
};

export const ShoppingList = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [currentOffer, setCurrentOffer] = useState(product.offers[0]);

  const tabs = [
    {
      label: 'Your Custom Lists',
      onClick: (index: number) => setActiveTab(index),
    },
    {
      label: 'Previously Purchased',
      onClick: (index: number) => setActiveTab(index),
    },
  ];

  const handleSwapVendor = (newOfferId: string) => {
    setCurrentOffer(product.offers.find(({ id }) => newOfferId === id)!);
  };

  const { salePrice, originalPrice } =
    getProductOfferComputedData(currentOffer);

  return (
    <div className="m-6 mt-8 flex h-full flex-col items-center rounded-sm border border-black/[0.06] bg-white p-6">
      <h1 className="text-lg font-semibold">Your Shopping Lists</h1>
      <div className="mt-5 w-lg font-medium">
        <Tabs active={activeTab} tabs={tabs} />
      </div>
      <div className="mt-6 w-full rounded-sm bg-gray-100/50 p-4">
        {activeTab === 0 && (
          <Item
            product={product}
            currentOfferId={currentOffer.id}
            onSwap={handleSwapVendor}
            originalPrice={originalPrice}
            salePrice={salePrice}
            currentOffer={currentOffer}
          />
        )}
        {activeTab === 1 && (
          <div className="text-center text-gray-600">
            Previously Purchased items
          </div>
        )}
      </div>
    </div>
  );
};
